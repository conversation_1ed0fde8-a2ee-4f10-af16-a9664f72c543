import React from 'react';
import { HomeIcon } from 'lucide-react';
import Header from './Header';
import topicsData from '../data/topics.json';

const TopicSelection = ({
  onSelectTopic
}) => {
  const { topics } = topicsData;
  
  return <div className="w-full min-h-screen">
      <Header title="Topic Selection" showHomeButton={false} />
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="mb-8 text-center">
          <h2 className="text-2xl font-bold text-gray-800 mb-2">
            Choose a topic
          </h2>
          <p className="text-gray-600">
            Select a topic to begin writing your essay
          </p>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
          {topics.map(topic => <button key={topic.id} className="bg-white border border-gray-200 rounded-lg p-6 text-left hover:bg-blue-50 hover:border-blue-300 transition duration-200 shadow-sm" onClick={() => onSelectTopic(topic.title)}>
              <h3 className="font-medium text-lg text-gray-800">
                {topic.title}
              </h3>
            </button>)}
        </div>
        <div className="text-center">
          <button onClick={() => onSelectTopic('Custom Topic')} className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition duration-200">
            Begin Essay / Mula Menulis
          </button>
        </div>
      </div>
    </div>;
};
export default TopicSelection;