import React from 'react';
import { HomeIcon, ChevronLeftIcon } from 'lucide-react';
const Header = ({
  title,
  topic = '',
  showHomeButton = true,
  onBackToHome = () => {}
}) => {
  return <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 py-4 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {showHomeButton && <button onClick={onBackToHome} className="flex items-center text-gray-600 hover:text-blue-600">
              <HomeIcon size={20} className="mr-1" />
              <span>Home</span>
            </button>}
          {topic && <div className="flex items-center">
              <ChevronLeftIcon size={20} className="text-gray-400" />
              <span className="ml-2 font-medium text-gray-800">
                Topic: {topic}
              </span>
            </div>}
        </div>
        <div className="text-xl font-bold text-gray-800">{title}</div>
        <div className="w-24">
          {/* Placeholder for right side content if needed */}
        </div>
      </div>
    </header>;
};
export default Header;