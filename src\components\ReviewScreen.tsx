import React from 'react';
import Header from './Header';
import { ArrowLeftIcon, PrinterIcon, DownloadIcon } from 'lucide-react';
const ReviewScreen = ({
  topic,
  essayContent,
  onBackToEssay,
  onBackToHome
}) => {
  // Mock feedback - in a real application this would come from an AI service
  const feedback = {
    vocabulary: ["Consider using 'serta' instead of 'dan' to vary your linking words.", 'Try using more descriptive adjectives in your introduction.'],
    grammar: ['Check the word order in paragraph 2.', "Remember to use correct verb forms after 'sedang'."],
    structure: ['Your conclusion could more clearly summarize your main points.', 'Try to connect your ideas between paragraphs more smoothly.']
  };
  return <div className="w-full min-h-screen flex flex-col">
      <Header title="Review Essay" topic={topic} onBackToHome={onBackToHome} />
      <div className="flex-grow p-4 pb-16 bg-gray-50 overflow-y-auto">
        <div className="max-w-4xl mx-auto">
          <div className="flex justify-between items-center mb-6">
            <button onClick={onBackToEssay} className="flex items-center text-blue-600 hover:text-blue-800">
              <ArrowLeftIcon size={18} className="mr-1" />
              Back to Editor
            </button>
            <div className="flex space-x-4">
              <button className="flex items-center text-gray-600 hover:text-gray-800">
                <PrinterIcon size={18} className="mr-1" />
                Print
              </button>
              <button className="flex items-center text-gray-600 hover:text-gray-800">
                <DownloadIcon size={18} className="mr-1" />
                Download
              </button>
              <button onClick={onBackToHome} className="flex items-center text-gray-600 hover:text-gray-800">
                <div size={18} className="mr-1" />
                Start New Essay
              </button>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-md p-8 mb-6">
            <h2 className="text-2xl font-bold text-gray-800 mb-4 text-center">
              {topic}
            </h2>
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-2">Introduction</h3>
              <p className="whitespace-pre-wrap">
                {essayContent.introduction || 'No introduction written.'}
              </p>
            </div>
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-2">Main Points</h3>
              {essayContent.mainPoints.map((point, index) => <div key={index} className="mb-4">
                  <h4 className="font-medium">Point {index + 1}</h4>
                  <p className="whitespace-pre-wrap">
                    {point || 'No content for this point.'}
                  </p>
                </div>)}
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-2">Conclusion</h3>
              <p className="whitespace-pre-wrap">
                {essayContent.conclusion || 'No conclusion written.'}
              </p>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 className="text-lg font-semibold mb-4">Feedback (English)</h3>
            <div className="mb-4">
              <h4 className="font-medium text-blue-700 mb-2">Vocabulary</h4>
              <ul className="list-disc pl-5 space-y-1">
                {feedback.vocabulary.map((item, index) => <li key={index}>{item}</li>)}
              </ul>
            </div>
            <div className="mb-4">
              <h4 className="font-medium text-red-700 mb-2">Grammar</h4>
              <ul className="list-disc pl-5 space-y-1">
                {feedback.grammar.map((item, index) => <li key={index}>{item}</li>)}
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-green-700 mb-2">Structure</h4>
              <ul className="list-disc pl-5 space-y-1">
                {feedback.structure.map((item, index) => <li key={index}>{item}</li>)}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>;
};
export default ReviewScreen;