import React, { useState, useEffect } from 'react';
import { BookIcon, GraduationCap, ChevronRight, ChevronLeft, Send } from 'lucide-react';
import { getTranslationResponse } from '../utils/translationClient';
import { getTutorResponse } from '../utils/tutorClient';
import { logger } from '../utils/debugLogger';

interface Response {
  type: 'system' | 'user' | 'ai';
  content: string;
}

interface AiTutorSidebarProps {
  collapsed: boolean;
  onToggleCollapse: () => void;
  mode: 'translation' | 'tutor';
  onChangeMode: (mode: 'translation' | 'tutor') => void;
}

const AiTutorSidebar: React.FC<AiTutorSidebarProps> = ({
  collapsed,
  onToggleCollapse,
  mode,
  onChangeMode
}) => {
  const [question, setQuestion] = useState('');
  const [responses, setResponses] = useState<Response[]>([{
    type: 'system',
    content: mode === 'translation' ? 'Ask me to translate words or phrases from English to Malay!' : 'I\'m your essay writing tutor! Ask me anything about writing your essay!'
  }]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    logger.info('AiTutorSidebar', '🎯 Mode changed', { mode });
    setResponses([{
      type: 'system',
      content: mode === 'translation' ? 'Ask me to translate words or phrases from English to Malay!' : 'I\'m your essay writing tutor! Ask me anything about writing your essay!'
    }]);
  }, [mode]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!question.trim() || isLoading) {
      logger.warn('AiTutorSidebar', '⚠️ Invalid submission', { 
        questionLength: question.length, 
        isLoading 
      });
      return;
    }

    logger.info('AiTutorSidebar', '📝 Processing new question', { 
      mode, 
      question 
    });

    // Add user question to responses
    const userResponse: Response = {
      type: 'user',
      content: question
    };

    setResponses(prev => [...prev, userResponse]);
    setIsLoading(true);

    try {
      logger.info('AiTutorSidebar', '🔄 Preparing conversation history');
      
      // Get conversation history excluding system messages
      const conversationHistory = responses
        .filter(r => r.type !== 'system')
        .map(r => ({
          role: r.type === 'user' ? 'user' as const : 'assistant' as const,
          content: r.content
        }));
      
      logger.info('AiTutorSidebar', '📚 Conversation history prepared', { 
        historyLength: conversationHistory.length 
      });

      // Get AI response from the appropriate API based on mode
      const aiResponse = mode === 'translation'
        ? await getTranslationResponse(question, conversationHistory)
        : await getTutorResponse(question, conversationHistory);

      setResponses(prev => [...prev, {
        type: 'ai',
        content: aiResponse
      }]);
    } catch (error) {
      logger.error('AiTutorSidebar', '❌ Error processing request', { 
        error: error instanceof Error ? error.message : error 
      });
      
      setResponses(prev => [...prev, {
        type: 'ai',
        content: 'Sorry, I encountered an error. Please try again.'
      }]);
    } finally {
      logger.info('AiTutorSidebar', '✅ Request completed');
      setIsLoading(false);
      setQuestion('');
    }
  };

  if (collapsed) {
    return (
      <button 
        onClick={onToggleCollapse} 
        className="bg-white border-l border-gray-200 p-2 hover:bg-gray-50"
      >
        <ChevronLeft size={20} className="text-gray-600" />
      </button>
    );
  }
  return (
    <div className="w-1/4 bg-white border-l border-gray-200 flex flex-col">
      <div className="p-4 border-b border-gray-200 flex justify-between items-center">
        <h3 className="font-medium text-gray-800">AI Tutor</h3>
        <button onClick={onToggleCollapse} className="text-gray-500 hover:text-gray-700">
          <ChevronRight size={20} />
        </button>
      </div>
      <div className="p-4 border-b border-gray-200">
        <div className="flex space-x-2">
          <button onClick={() => onChangeMode('tutor')} className={`flex-1 py-2 px-3 rounded-md flex items-center justify-center ${mode === 'tutor' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}>
            <GraduationCap size={16} className="mr-2" />
            <span className="text-sm">Tutor</span>
          </button>
          <button onClick={() => onChangeMode('translation')} className={`flex-1 py-2 px-3 rounded-md flex items-center justify-center ${mode === 'translation' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}>
            <BookIcon size={16} className="mr-2" />
            <span className="text-sm">Translation</span>
          </button>
        </div>
      </div>
      <div className="flex-grow overflow-y-auto p-4">
        <div className="space-y-4">
          {responses.map((response, index) => <div key={index} className={`p-3 rounded-lg ${response.type === 'user' ? 'bg-blue-50 ml-6' : response.type === 'ai' ? 'bg-gray-50 mr-6' : 'bg-gray-50 border border-gray-200 text-center text-sm text-gray-500'}`}>
              {response.content}
            </div>)}
        </div>
      </div>
      <form onSubmit={handleSubmit} className="p-4 border-t border-gray-200">
        <div className="flex items-center">
          <input type="text" value={question} onChange={e => setQuestion(e.target.value)} placeholder={mode === 'translation' ? 'Ask for a translation...' : 'Ask for essay help...'} className="flex-grow rounded-l-md border border-gray-300 py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-100 focus:border-blue-400" />
          <button type="submit" className="bg-blue-600 hover:bg-blue-700 text-white rounded-r-md p-2 h-[42px]">
            <Send size={18} />
          </button>
        </div>
      </form>
    </div>
  );
};

export default AiTutorSidebar;