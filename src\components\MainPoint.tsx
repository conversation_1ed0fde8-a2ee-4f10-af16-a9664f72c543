import React from 'react';
import { XCircleIcon } from 'lucide-react';
const MainPoint = ({
  number,
  content,
  onChange,
  onRemove,
  showRemoveButton
}) => {
  return <div className="border border-gray-300 rounded-lg overflow-hidden focus-within:border-blue-400 focus-within:ring-2 focus-within:ring-blue-100 transition duration-200">
      <div className="bg-gray-50 px-4 py-2 border-b border-gray-200 flex justify-between items-center">
        <span className="text-sm text-gray-600">
          Isi Utama {number} (Main Point {number})
        </span>
        {showRemoveButton && <button onClick={onRemove} className="text-gray-400 hover:text-red-500">
            <XCircleIcon size={16} />
          </button>}
      </div>
      <textarea value={content} onChange={e => onChange(e.target.value)} className="w-full p-4 min-h-[120px] focus:outline-none resize-none" placeholder={`Tulis isi utama ${number} di sini... (Write your main point ${number} here...)`} />
    </div>;
};
export default MainPoint;