import React, { useState } from 'react';
import Header from './Header';
import AiTutorSidebar from './AiTutorSidebar';
import Introduction from './Introduction';
import MainPoint from './MainPoint';
import Conclusion from './Conclusion';
import { PlusCircleIcon, MessageCircleQuestionIcon } from 'lucide-react';
import { getTutorResponse } from '../utils/tutorClient';
import ReactMarkdown from 'react-markdown';

const EssayCanvas = ({
  topic,
  essayContent,
  setEssayContent,
  onSubmit,
  onBackToHome
}) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [sidebarMode, setSidebarMode] = useState('tutor');
  const [introFeedback, setIntroFeedback] = useState('');
  const [mainPointsFeedback, setMainPointsFeedback] = useState<string[]>([]);
  const [conclusionFeedback, setConclusionFeedback] = useState('');

  const handleIntroductionChange = text => {
    setEssayContent({
      ...essayContent,
      introduction: text
    });
  };
  const handleMainPointChange = (index, text) => {
    const updatedMainPoints = [...essayContent.mainPoints];
    updatedMainPoints[index] = text;
    setEssayContent({
      ...essayContent,
      mainPoints: updatedMainPoints
    });
  };
  const handleConclusionChange = text => {
    setEssayContent({
      ...essayContent,
      conclusion: text
    });
  };
  const addMainPoint = () => {
    setEssayContent({
      ...essayContent,
      mainPoints: [...essayContent.mainPoints, '']
    });
    setMainPointsFeedback([...mainPointsFeedback, '']);
  };
  const removeMainPoint = index => {
    const updatedMainPoints = essayContent.mainPoints.filter((_, i) => i !== index);
    setEssayContent({
      ...essayContent,
      mainPoints: updatedMainPoints
    });
    setMainPointsFeedback(mainPointsFeedback.filter((_, i) => i !== index));
  };

  // Helper to build context for TutorAI
  const buildContext = () => ({
    topic,
    introduction: essayContent.introduction,
    mainPoints: essayContent.mainPoints,
    conclusion: essayContent.conclusion,
  });

  // TutorAI feedback function using API
  const getTutorAIFeedback = async (
    section: string,
    paragraph: string,
    context: any,
    previousMessages: { role: 'user' | 'assistant'; content: string }[] = []
  ) => {
    if (!paragraph || paragraph.trim().length === 0) {
      return `Please write your ${section} first.`;
    }
    // Compose a feedback prompt for the AI
    let prompt = '';
    if (section.startsWith('main point')) {
      prompt = `Essay Topic: ${context.topic}\n\nIntroduction: ${context.introduction}\n\nMain Point: ${paragraph}\n\nOther Main Points: ${context.mainPoints.filter(mp => mp !== paragraph).join(' | ')}\n\nConclusion: ${context.conclusion}\n\nPlease provide feedback and corrections for the above main point in the context of the essay.`;
    } else if (section === 'introduction') {
      prompt = `Essay Topic: ${context.topic}\n\nIntroduction: ${paragraph}\n\nMain Points: ${context.mainPoints.join(' | ')}\n\nConclusion: ${context.conclusion}\n\nPlease provide feedback and corrections for the above introduction in the context of the essay.`;
    } else if (section === 'conclusion') {
      prompt = `Essay Topic: ${context.topic}\n\nIntroduction: ${context.introduction}\n\nMain Points: ${context.mainPoints.join(' | ')}\n\nConclusion: ${paragraph}\n\nPlease provide feedback and corrections for the above conclusion in the context of the essay.`;
    } else {
      prompt = paragraph;
    }
    // Pass previousMessages to getTutorResponse
    return getTutorResponse(prompt, previousMessages, true);
  };

  const handleCheckIntroduction = async () => {
    setIntroFeedback('Checking introduction...');
    const context = buildContext();
    // You can manage previousMessages state if you want to keep history
    const feedback = await getTutorAIFeedback('introduction', essayContent.introduction, context, []);
    setIntroFeedback(feedback);
  };

  const handleCheckMainPoint = async (index: number) => {
    const updatedFeedback = [...mainPointsFeedback];
    updatedFeedback[index] = 'Checking main point...';
    setMainPointsFeedback(updatedFeedback);
    const context = buildContext();
    const feedback = await getTutorAIFeedback(`main point ${index + 1}`, essayContent.mainPoints[index], context, []);
    updatedFeedback[index] = feedback;
    setMainPointsFeedback([...updatedFeedback]);
  };

  const handleCheckConclusion = async () => {
    setConclusionFeedback('Checking conclusion...');
    const context = buildContext();
    const feedback = await getTutorAIFeedback('conclusion', essayContent.conclusion, context, []);
    setConclusionFeedback(feedback);
  };

  return (
    <div className="w-full min-h-screen flex flex-col">
      <Header title="Essay Writing" topic={topic} onBackToHome={onBackToHome} />
      <div className="flex flex-grow">
        <div className={`flex-grow p-4 pb-16 bg-gray-50 overflow-y-auto ${sidebarCollapsed ? 'w-full' : 'w-3/4'}`}>
          <div className="max-w-3xl mx-auto bg-white rounded-lg shadow p-6">
            <div className="mb-6">
              <div className="text-center mb-4">
                <h2 className="text-lg font-medium text-gray-700">
                  Write your essay in Malay. Include Introduction, Main Points,
                  and Conclusion.
                </h2>
              </div>
              {/* Introduction */}
              <div className="mb-8">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-lg font-bold text-gray-800 uppercase tracking-wider">
                    Introduction
                  </h3>
                  <button
                    onClick={handleCheckIntroduction}
                    className="p-2 rounded-full hover:bg-blue-100 transition"
                    title="Get feedback on your introduction"
                    type="button"
                  >
                    <MessageCircleQuestionIcon size={22} className="text-blue-600" />
                  </button>
                </div>
                <Introduction content={essayContent.introduction} onChange={handleIntroductionChange} />
                {introFeedback && (
                  <div className="mt-2 text-sm text-blue-700 bg-blue-50 rounded p-2">
                    <ReactMarkdown>{introFeedback}</ReactMarkdown>
                  </div>
                )}
              </div>
              {/* Main Points */}
              <div className="mb-8">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-lg font-bold text-gray-800 uppercase tracking-wider">
                    Main Points
                  </h3>
                  <button onClick={addMainPoint} className="flex items-center text-sm text-blue-600 hover:text-blue-800">
                    <PlusCircleIcon size={16} className="mr-1" />
                    Add Main Point
                  </button>
                </div>
                {essayContent.mainPoints.map((point, index) => (
                  <div key={index} className="mb-4">
                    <div className="flex items-center justify-between mb-1">
                      <span className="font-semibold text-gray-700">Main Point {index + 1}</span>
                      <button
                        onClick={() => handleCheckMainPoint(index)}
                        className="p-2 rounded-full hover:bg-blue-100 transition"
                        title="Get feedback on this main point"
                        type="button"
                      >
                        <MessageCircleQuestionIcon size={20} className="text-blue-600" />
                      </button>
                    </div>
                    <MainPoint
                      number={index + 1}
                      content={point}
                      onChange={text => handleMainPointChange(index, text)}
                      onRemove={() => removeMainPoint(index)}
                      showRemoveButton={essayContent.mainPoints.length > 1}
                    />
                    {mainPointsFeedback[index] && (
                      <div className="mt-1 text-sm text-blue-700 bg-blue-50 rounded p-2">
                        <ReactMarkdown>{mainPointsFeedback[index]}</ReactMarkdown>
                      </div>
                    )}
                  </div>
                ))}
              </div>
              {/* Conclusion */}
              <div className="mb-8">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-lg font-bold text-gray-800 mb-2 uppercase tracking-wider">
                    Conclusion
                  </h3>
                  <button
                    onClick={handleCheckConclusion}
                    className="p-2 rounded-full hover:bg-blue-100 transition"
                    title="Get feedback on your conclusion"
                    type="button"
                  >
                    <MessageCircleQuestionIcon size={22} className="text-blue-600" />
                  </button>
                </div>
                <Conclusion content={essayContent.conclusion} onChange={handleConclusionChange} />
                {conclusionFeedback && (
                  <div className="mt-2 text-sm text-blue-700 bg-blue-50 rounded p-2">
                    <ReactMarkdown>{conclusionFeedback}</ReactMarkdown>
                  </div>
                )}
              </div>
              <div className="text-center mt-8">
                <button onClick={onSubmit} className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition duration-200">
                  Review & Submit
                </button>
              </div>
            </div>
          </div>
        </div>
        <AiTutorSidebar collapsed={sidebarCollapsed} onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)} mode={sidebarMode} onChangeMode={setSidebarMode} />
      </div>
    </div>
  );
};

export default EssayCanvas;