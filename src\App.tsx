import React, { useState } from 'react';
import TopicSelection from './components/TopicSelection';
import EssayCanvas from './components/EssayCanvas';
import ReviewScreen from './components/ReviewScreen';
export function App() {
  const [currentScreen, setCurrentScreen] = useState('topic-selection');
  const [selectedTopic, setSelectedTopic] = useState('');
  const [essayContent, setEssayContent] = useState({
    introduction: '',
    mainPoints: [''],
    conclusion: ''
  });
  const handleTopicSelect = topic => {
    setSelectedTopic(topic);
    setCurrentScreen('essay-canvas');
  };
  const handleReviewSubmit = () => {
    setCurrentScreen('review');
  };
  const handleBackToHome = () => {
    setCurrentScreen('topic-selection');
    setSelectedTopic('');
    setEssayContent({
      introduction: '',
      mainPoints: [''],
      conclusion: ''
    });
  };
  const handleBackToEssay = () => {
    setCurrentScreen('essay-canvas');
  };
  return <div className="w-full min-h-screen bg-gray-50">
      {currentScreen === 'topic-selection' && <TopicSelection onSelectTopic={handleTopicSelect} />}
      {currentScreen === 'essay-canvas' && <EssayCanvas topic={selectedTopic} essayContent={essayContent} setEssayContent={setEssayContent} onSubmit={handleReviewSubmit} onBackToHome={handleBackToHome} />}
      {currentScreen === 'review' && <ReviewScreen topic={selectedTopic} essayContent={essayContent} onBackToEssay={handleBackToEssay} onBackToHome={handleBackToHome} />}
    </div>;
}