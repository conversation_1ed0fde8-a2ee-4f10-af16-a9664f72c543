const LOG_PREFIX = '[BM Essay Tutor]';

export const logger = {
  debug: (component: string, message: string, data?: any) => {
    // Force log to console and ensure it shows in Vite dev server
    console.log(`${LOG_PREFIX} [${component}] ${message}`, data ? data : '');
  },
    error: (component: string, message: string, error?: any) => {
    // Force error to console and ensure it shows in Vite dev server
    console.error(`${LOG_PREFIX} [${component}] ${message}`, error ? error : '');
  },

  info: (component: string, message: string, data?: any) => {
    // Force info to console and ensure it shows in Vite dev server
    console.info(`${LOG_PREFIX} [${component}] ${message}`, data ? data : '');
  },

  warn: (component: string, message: string, data?: any) => {
    // Force warning to console and ensure it shows in Vite dev server
    console.warn(`${LOG_PREFIX} [${component}] ${message}`, data ? data : '');
  }
};
